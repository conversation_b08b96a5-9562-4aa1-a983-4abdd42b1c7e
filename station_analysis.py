#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
新建场站决策辅助功能 - 阶段一开发
基于POI数据的场站相似度分析与评分框架

此脚本实现了:
1. 读取现有场站POI数据
2. 构建多维度评分框架
3. 计算场站间的相似度
4. 预测新场站位置的适合度
"""

import os
import json
import pandas as pd
import numpy as np
import math
import requests
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
import matplotlib.pyplot as plt
from order_analysis import OrderAnalyzer
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
from collections import defaultdict
import re

class StationAnalyzer:
    """场站分析器类，用于分析场站POI数据并构建评分框架"""

    def __init__(self, order_data_file='resource/2025_Q2_10cols_107stations.csv', order_data_file_q1='resource/output_2025_Q1_10cols_107stations.csv'):
        """初始化场站分析器

        参数:
            order_data_file: 第二季度订单数据文件路径
            order_data_file_q1: 第一季度订单数据文件路径
        """
        self.station_data = None
        self.poi_categories = None
        self.used_poi_codes = None
        self.raw_poi_data = {}
        self.poi_vectors = None
        self.similarity_matrix = None
        self.weights = None
        self.similarity_df = None
        self.scores_df = None

        # 订单数据相关
        self.order_data_file = order_data_file
        self.order_data_file_q1 = order_data_file_q1
        self.order_analyzer = None
        self.order_analyzer_q1 = None
        self.performance_scores = None
        self.optimized_weights = None
        self.use_two_quarters = True  # 是否使用两个季度的数据

        # 战略价值评分相关
        self.strategic_scores = None
        self.api_key = '9bc017d3ba3c32eb6fb2d9fe0d224d96'  # 高德地图API密钥

        # API请求缓存
        self.poi_cache = {}  # 用于缓存POI请求结果

    def load_data(self):
        """加载所有必要的数据文件"""
        print("正在加载数据...")

        # 加载场站POI数据
        self.station_data = pd.read_excel('resource/场站POI数据_valid.xlsx')
        print(f"已加载场站数据，共 {len(self.station_data)} 条记录")

        # 加载POI分类编码
        self.poi_categories = pd.read_excel('resource/高德POI分类与编码（中英文）_V1.06_20230208.xlsx')
        print(f"已加载POI分类编码，共 {len(self.poi_categories)} 条记录")

        # 加载raw_poi_data文件夹中的JSON数据
        self._load_raw_poi_data()

        # 提取使用的POI编码
        self._extract_used_poi_codes()

        # 初始化订单分析器
        self._init_order_analyzer()

    def _init_order_analyzer(self):
        """初始化订单分析器"""
        # 初始化第二季度订单分析器
        if os.path.exists(self.order_data_file):
            try:
                self.order_analyzer = OrderAnalyzer(self.order_data_file)
                print(f"已初始化第二季度订单分析器，使用数据文件: {self.order_data_file}")
            except Exception as e:
                print(f"初始化第二季度订单分析器时出错: {e}")
                self.order_analyzer = None
        else:
            print(f"订单数据文件 {self.order_data_file} 不存在，跳过订单分析")
            self.order_analyzer = None

        # 初始化第一季度订单分析器
        if self.use_two_quarters and os.path.exists(self.order_data_file_q1):
            try:
                self.order_analyzer_q1 = OrderAnalyzer(self.order_data_file_q1)
                print(f"已初始化第一季度订单分析器，使用数据文件: {self.order_data_file_q1}")
            except Exception as e:
                print(f"初始化第一季度订单分析器时出错: {e}")
                self.order_analyzer_q1 = None
        else:
            if self.use_two_quarters:
                print(f"订单数据文件 {self.order_data_file_q1} 不存在，将只使用第二季度数据")
            self.order_analyzer_q1 = None

    def _load_raw_poi_data(self):
        """加载raw_poi_data文件夹中的JSON数据"""
        raw_poi_dir = 'raw_poi_data'
        count = 0

        for filename in os.listdir(raw_poi_dir):
            if filename.endswith('.json'):
                file_path = os.path.join(raw_poi_dir, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    # 从文件名中提取场站名称和坐标
                    match = re.match(r'(.+)_(\d+\.\d+)_(\d+\.\d+)_.*\.json', filename)
                    if match:
                        station_name = match.group(1)
                        longitude = float(match.group(2))
                        latitude = float(match.group(3))

                        self.raw_poi_data[station_name] = {
                            'data': data,
                            'longitude': longitude,
                            'latitude': latitude
                        }
                        count += 1
                except Exception as e:
                    print(f"加载 {filename} 时出错: {e}")

        print(f"已加载 {count} 个场站的原始POI数据")

    def _extract_used_poi_codes(self):
        """提取需要使用的POI编码"""
        used_typecodes = set()

        # 1. 从 resource/weightPOI 文件中读取有效的POI编码
        weight_poi_file = 'resource/weightPOI'
        try:
            with open(weight_poi_file, 'r', encoding='utf-8') as f:
                for line in f:
                    code = line.strip()
                    if code:  # 如果不是空行
                        used_typecodes.add(code)
            print(f"从 {weight_poi_file} 文件中读取了 {len(used_typecodes)} 个有效的POI编码")
        except Exception as e:
            print(f"读取 {weight_poi_file} 文件时出错: {e}")

        # 2. 从POI权重文件中读取编码
        poi_weights_file = 'output/poi_weights.json'
        if os.path.exists(poi_weights_file):
            try:
                with open(poi_weights_file, 'r', encoding='utf-8') as f:
                    poi_weights = json.load(f)
                    for code in poi_weights.keys():
                        used_typecodes.add(code)
                print(f"从 {poi_weights_file} 加载了 {len(poi_weights)} 个POI权重")
            except Exception as e:
                print(f"加载POI权重文件失败: {e}")

        # 3. 如果仍然没有足够的编码，从现有的POI数据中提取
        if len(used_typecodes) < 10:  # 如果编码太少
            print("将使用场站数据中的所有POI编码")
            for station_name, station_info in self.raw_poi_data.items():
                pois = station_info['data'].get('pois', [])
                for poi in pois:
                    if 'typecode' in poi:
                        used_typecodes.add(poi['typecode'])

        # 将有效的POI编码转换为列表
        self.used_poi_codes = list(used_typecodes)
        print(f"最终使用 {len(self.used_poi_codes)} 个有效的POI编码")

        # 构建权重字典
        self.weights = self._build_weights_from_used_poi()

    def _build_weights_from_used_poi(self):
        """根据使用的POI编码构建权重字典，确保大类向下兼容所有中类和小类"""
        # 先检查是否存在权重文件，如果存在则直接读取
        weights_file = 'output/poi_weights.json'
        if os.path.exists(weights_file):
            try:
                with open(weights_file, 'r', encoding='utf-8') as f:
                    weights = json.load(f)
                    print(f"从 {weights_file} 加载了 {len(weights)} 个POI权重")
                    return weights
            except Exception as e:
                print(f"读取权重文件时出错: {e}")
                print("将重新构建权重字典")

        # 初始化权重字典
        weights = {}

        # 首先定义基础权重值
        base_weights = {
            # 交通设施相关
            '150000': 1.0,  # 交通设施服务
            '150100': 2.0,  # 公交车站
            '150200': 1.5,  # 地铁站
            '150300': 1.2,  # 长途汽车站
            '150400': 1.0,  # 火车站
            '150500': 0.9,  # 机场
            '150700': 1.0,  # 高速路出口
            '150800': 0.9,  # 服务区
            '150900': 0.8,  # 停车场

            # 商业设施相关
            '050000': 1.2,  # 餐饮服务
            '050100': 1.3,  # 中餐厅
            '050200': 1.2,  # 外国餐厅
            '050300': 1.1,  # 快餐店
            '050500': 1.0,  # 咖啡厅
            '060000': 1.0,  # 购物
            '060100': 1.2,  # 商场
            '060200': 1.1,  # 超市/便利店
            '060400': 0.9,  # 家居建材
            '070000': 0.8,  # 生活服务
            '070200': 0.9,  # 汽车服务
            '070500': 0.8,  # 邮局
            '080000': 0.7,  # 体育休闲
            '080100': 0.8,  # 体育场馆
            '080300': 0.7,  # 休闲娱乐
            '080500': 0.6,  # 度假村
            '100000': 0.6,  # 医疗保健
            '100100': 0.7,  # 医院
            '100200': 0.6,  # 诊所
            '100300': 0.5,  # 药店

            # 居住区相关
            '120000': 0.8,  # 商务住宅
            '120100': 0.9,  # 产业园区
            '120200': 1.4,  # 住宅区
            '120201': 1.5,  # 住宅区
            '120300': 1.2,  # 学校
            '120400': 0.9,  # 楼宇

            # 其他类别
            '010000': 1.5,  # 汽车服务
            '010100': 1.8,  # 加油站
            '011100': 2.0,  # 充电站
        }

        # 将基础权重值添加到权重字典中
        weights.update(base_weights)

        # 从高德POI分类编码中提取大类、中类和小类的关系
        category_hierarchy = {}

        # 如果有POI分类编码数据，则使用它来构建分类层次关系
        if hasattr(self, 'poi_categories') and self.poi_categories is not None:
            # 使用'NEW_TYPE'列作为POI编码
            if 'NEW_TYPE' in self.poi_categories.columns:
                for _, row in self.poi_categories.iterrows():
                    code = row['NEW_TYPE']
                    if isinstance(code, str) and len(code) == 6:  # 确保是6位编码
                        large_category = code[:2] + '0000'  # 大类
                        medium_category = code[:4] + '00'   # 中类

                        # 添加到分类层次关系中
                        if large_category not in category_hierarchy:
                            category_hierarchy[large_category] = {'medium': set(), 'small': set()}

                        category_hierarchy[large_category]['medium'].add(medium_category)
                        category_hierarchy[large_category]['small'].add(code)

                        if medium_category not in category_hierarchy:
                            category_hierarchy[medium_category] = {'small': set()}

                        category_hierarchy[medium_category]['small'].add(code)

        # 遍历使用的POI编码
        for code in self.used_poi_codes:
            if not isinstance(code, str) or len(code) != 6:  # 跳过非6位编码
                continue

            large_category = code[:2] + '0000'  # 大类
            medium_category = code[:4] + '00'   # 中类

            # 如果大类在使用的POI编码中，则将其权重应用到所有中类和小类
            if large_category in self.used_poi_codes and large_category in base_weights:
                large_weight = base_weights[large_category]

                # 如果大类在分类层次关系中
                if large_category in category_hierarchy:
                    # 将大类权重应用到所有中类（除非中类有自己的权重）
                    for medium in category_hierarchy[large_category]['medium']:
                        if medium not in base_weights:
                            weights[medium] = large_weight

                    # 将大类权重应用到所有小类（除非小类有自己的权重）
                    for small in category_hierarchy[large_category]['small']:
                        if small not in base_weights:
                            weights[small] = large_weight

            # 如果中类在使用的POI编码中，则将其权重应用到所有小类
            if medium_category in self.used_poi_codes and medium_category in base_weights:
                medium_weight = base_weights[medium_category]

                # 如果中类在分类层次关系中
                if medium_category in category_hierarchy:
                    # 将中类权重应用到所有小类（除非小类有自己的权重）
                    for small in category_hierarchy[medium_category]['small']:
                        if small not in base_weights:
                            weights[small] = medium_weight

            # 如果小类在使用的POI编码中，但没有自己的权重，则尝试使用中类或大类的权重
            if code in self.used_poi_codes and code not in weights:
                if medium_category in weights:
                    weights[code] = weights[medium_category]
                elif large_category in weights:
                    weights[code] = weights[large_category]
                else:
                    weights[code] = 0.5  # 默认权重

        # 将权重字典保存到文件
        try:
            # 确保 output 目录存在
            os.makedirs('output', exist_ok=True)
            with open(weights_file, 'w', encoding='utf-8') as f:
                json.dump(weights, f, ensure_ascii=False, indent=4)
            print(f"已将 {len(weights)} 个POI权重保存到 {weights_file}")
        except Exception as e:
            print(f"保存权重文件时出错: {e}")

        return weights

    def build_poi_vectors(self):
        """为每个场站构建POI向量，按照具体编码 > 中类编码 > 大类编码的层次结构进行数量相加"""
        print("正在构建POI向量...")

        # 创建一个字典，用于存储每个场站的POI向量
        station_poi_vectors = {}

        # 对于每个场站
        for station_name, station_info in self.raw_poi_data.items():
            # 初始化POI向量，每个POI类别的数量为0
            poi_vector = defaultdict(int)

            # 获取场站周围的POI数据
            pois = station_info['data'].get('pois', [])

            # 统计每个POI类别的数量，并按层次结构进行数量相加
            for poi in pois:
                if 'typecode' in poi:
                    typecode = poi['typecode']

                    # 获取大类和中类编码
                    if len(typecode) == 6:  # 确保是6位编码
                        large_category = typecode[:2] + '0000'  # 大类
                        medium_category = typecode[:4] + '00'   # 中类

                        # 先检查具体编码是否在有效编码中
                        if typecode in self.used_poi_codes:
                            poi_vector[typecode] += 1

                        # 然后检查中类编码是否在有效编码中
                        if medium_category in self.used_poi_codes:
                            poi_vector[medium_category] += 1

                        # 最后检查大类编码是否在有效编码中
                        if large_category in self.used_poi_codes:
                            poi_vector[large_category] += 1

            # 将defaultdict转换为普通dict并存储
            station_poi_vectors[station_name] = dict(poi_vector)

        # 创建一个DataFrame，行是场站，列是POI类别
        all_typecodes = sorted(self.used_poi_codes)
        stations = sorted(station_poi_vectors.keys())

        # 创建一个空的DataFrame
        self.poi_vectors = pd.DataFrame(0, index=stations, columns=all_typecodes)

        # 填充DataFrame
        for station in stations:
            for typecode, count in station_poi_vectors[station].items():
                if typecode in all_typecodes:
                    self.poi_vectors.at[station, typecode] = count

        print(f"已为 {len(stations)} 个场站构建POI向量，包含 {len(all_typecodes)} 个POI类别")

        return self.poi_vectors

    def calculate_similarity(self):
        """计算场站之间的相似度"""
        print("正在计算场站相似度...")

        if self.poi_vectors is None:
            self.build_poi_vectors()

        # 标准化POI向量
        scaler = StandardScaler()
        scaled_vectors = scaler.fit_transform(self.poi_vectors)

        # 计算余弦相似度
        self.similarity_matrix = cosine_similarity(scaled_vectors)

        # 创建一个DataFrame来存储相似度矩阵
        self.similarity_df = pd.DataFrame(
            self.similarity_matrix,
            index=self.poi_vectors.index,
            columns=self.poi_vectors.index
        )

        print("场站相似度计算完成")
        return self.similarity_df

    def find_similar_stations(self, station_name, top_n=5):
        """找出与给定场站最相似的N个场站"""
        if self.similarity_df is None:
            self.calculate_similarity()

        if station_name not in self.similarity_df.index:
            print(f"场站 '{station_name}' 不在数据集中")
            return None

        # 获取与给定场站的相似度
        similarities = self.similarity_df[station_name].sort_values(ascending=False)

        # 排除自身
        similarities = similarities[similarities.index != station_name]

        # 返回前N个最相似的场站
        return similarities.head(top_n)

    def _process_new_station_poi(self, new_poi_data):
        """处理新场站的POI数据，构建POI向量"""
        # 解析新场站的POI数据，按照层次结构进行数量相加
        new_poi_vector = defaultdict(int)
        for poi in new_poi_data.get('pois', []):
            if 'typecode' in poi:
                typecode = poi['typecode']

                # 处理复合类型编码（如 "050201|080000"）
                if '|' in typecode:
                    typecodes = typecode.split('|')
                else:
                    typecodes = [typecode]

                for tc in typecodes:
                    # 获取大类和中类编码
                    if len(tc) == 6:  # 确保是6位编码
                        large_category = tc[:2] + '0000'  # 大类
                        medium_category = tc[:4] + '00'   # 中类

                        # 优先使用最具体的编码，避免重复计数
                        # 这是关键改进：只在最具体的层次计数一次
                        if tc in self.used_poi_codes:
                            new_poi_vector[tc] += 1
                        elif medium_category in self.used_poi_codes:
                            new_poi_vector[medium_category] += 1
                        elif large_category in self.used_poi_codes:
                            new_poi_vector[large_category] += 1

        # 创建一个与现有POI向量相同格式的向量
        new_vector = pd.Series(0, index=self.poi_vectors.columns)
        for typecode, count in new_poi_vector.items():
            if typecode in new_vector.index:
                new_vector[typecode] = count

        return new_vector

    def predict_new_station(self, new_poi_data, top_n=5):
        """预测新场站的适合度"""
        print("正在预测新场站的适合度...")

        if self.poi_vectors is None:
            self.build_poi_vectors()

        # 解析新场站的POI数据，按照层次结构进行数量相加
        new_poi_vector = defaultdict(int)
        for poi in new_poi_data.get('pois', []):
            if 'typecode' in poi:
                typecode = poi['typecode']

                # 获取大类和中类编码
                if len(typecode) == 6:  # 确保是6位编码
                    large_category = typecode[:2] + '0000'  # 大类
                    medium_category = typecode[:4] + '00'   # 中类

                    # 先检查具体编码是否在有效编码中
                    if typecode in self.used_poi_codes:
                        new_poi_vector[typecode] += 1

                    # 然后检查中类编码是否在有效编码中
                    if medium_category in self.used_poi_codes:
                        new_poi_vector[medium_category] += 1

                    # 最后检查大类编码是否在有效编码中
                    if large_category in self.used_poi_codes:
                        new_poi_vector[large_category] += 1

        # 创建一个与现有POI向量相同格式的向量
        new_vector = pd.DataFrame(0, index=['new_station'], columns=self.poi_vectors.columns)
        for typecode, count in new_poi_vector.items():
            if typecode in new_vector.columns:
                new_vector.at['new_station', typecode] = count

        # 将新向量与现有向量合并
        combined_vectors = pd.concat([self.poi_vectors, new_vector])

        # 标准化
        scaler = StandardScaler()
        scaled_vectors = scaler.fit_transform(combined_vectors)

        # 计算新场站与所有现有场站的相似度
        new_station_index = len(combined_vectors) - 1
        similarities = cosine_similarity([scaled_vectors[new_station_index]], scaled_vectors[:-1])[0]

        # 创建一个Series来存储相似度
        similarity_series = pd.Series(similarities, index=self.poi_vectors.index)

        # 返回前N个最相似的场站
        top_similar = similarity_series.sort_values(ascending=False).head(top_n)

        print(f"找到 {len(top_similar)} 个与新场站最相似的现有场站")
        return top_similar

    def _calculate_all_station_raw_scores(self):
        """计算所有场站的原始得分"""
        # 如果权重字典未初始化，则构建它
        if self.weights is None:
            self.weights = self._build_weights_from_used_poi()

        # 计算所有场站的原始得分
        raw_scores = {}
        for station in self.poi_vectors.index:
            score = 0
            poi_vector = self.poi_vectors.loc[station]
            for typecode, count in poi_vector.items():
                # 获取大类编码（前两位）
                large_category_code = typecode[:2] + '0000'

                # 获取中类编码（前四位）
                medium_category_code = typecode[:4] + '00'

                # 优先级：具体编码 > 中类编码 > 大类编码 > 默认权重
                weight = self.weights.get(typecode,
                          self.weights.get(medium_category_code,
                            self.weights.get(large_category_code, 0.5)))

                # 累加得分
                score += count * weight

            raw_scores[station] = score

        # 记录最高得分作为归一化基准
        self.max_raw_score = max(raw_scores.values()) if raw_scores else 1

        return raw_scores

    def evaluate_station_score(self, station_name):
        """评估场站的综合得分"""
        if station_name not in self.poi_vectors.index:
            print(f"场站 '{station_name}' 不在数据集中")
            return None

        # 获取场站的POI向量
        poi_vector = self.poi_vectors.loc[station_name]

        # 如果权重字典未初始化，则构建它
        if self.weights is None:
            self.weights = self._build_weights_from_used_poi()

        # 计算加权得分
        score = 0
        for typecode, count in poi_vector.items():
            # 获取大类编码（前两位）
            large_category_code = typecode[:2] + '0000'

            # 获取中类编码（前四位）
            medium_category_code = typecode[:4] + '00'

            # 优先级：具体编码 > 中类编码 > 大类编码 > 默认权重
            weight = self.weights.get(typecode,
                      self.weights.get(medium_category_code,
                        self.weights.get(large_category_code, 0.5)))

            # 累加得分
            score += count * weight

        # 如果还没有计算所有场站的原始得分，则计算它们
        if not hasattr(self, 'max_raw_score'):
            self._calculate_all_station_raw_scores()

        # 使用现有场站的最高得分作为归一化基准
        normalized_score = (score / self.max_raw_score) * 100 if self.max_raw_score > 0 else 0

        return normalized_score

    def calculate_and_save_strategic_scores(self, force_recalculate=False):
        """计算并保存所有充电站的战略价值评分

        参数:
            force_recalculate: 是否强制重新计算所有站点的战略价值评分

        返回:
            战略价值评分字典 {station_name: score}
        """
        import os
        import json

        strategic_scores = {}
        print("正在计算战略价值评分...")

        # 检查是否存在缓存文件
        cache_file = 'output/strategic_scores.json'
        if os.path.exists(cache_file) and not force_recalculate:
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    strategic_scores = json.load(f)
                print(f"已从缓存加载 {len(strategic_scores)} 个充电站的战略价值评分")

                # 检查是否需要计算新增的充电站
                missing_stations = [station for station in self.poi_vectors.index if station not in strategic_scores]
                if not missing_stations:
                    self.strategic_scores = strategic_scores
                    return strategic_scores

                print(f"需要计算 {len(missing_stations)} 个新增充电站的战略价值评分")
                for station in missing_stations:
                    strategic_scores[station] = self.calculate_strategic_value_score(station_name=station)
            except Exception as e:
                print(f"加载缓存文件失败: {e}")
                strategic_scores = {}

        # 如果没有缓存或缓存加载失败，计算所有充电站的战略价值评分
        if not strategic_scores or force_recalculate:
            for station in self.poi_vectors.index:
                strategic_scores[station] = self.calculate_strategic_value_score(station_name=station)

        # 确保输出目录存在
        os.makedirs('output', exist_ok=True)

        # 保存到文件
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(strategic_scores, f, ensure_ascii=False, indent=2)

        print(f"已计算并保存 {len(strategic_scores)} 个充电站的战略价值评分")
        self.strategic_scores = strategic_scores
        return strategic_scores

    def build_scoring_framework(self, calculate_strategic=True):
        """构建多维度评分框架

        参数:
            calculate_strategic: 是否计算战略价值评分
        """
        print("正在构建多维度评分框架...")

        if self.poi_vectors is None:
            self.build_poi_vectors()

        # 如果有订单数据，尝试优化权重
        if self.order_analyzer is not None:
            self.optimize_weights_with_orders()

        # 为每个场站计算POI评分
        poi_scores = {}
        for station in self.poi_vectors.index:
            poi_scores[station] = self.evaluate_station_score(station)

        # 计算战略价值评分（如果需要）
        if calculate_strategic:
            strategic_scores = self.calculate_and_save_strategic_scores()
        else:
            # 尝试从缓存加载战略价值评分
            import os
            import json
            cache_file = 'output/strategic_scores.json'
            if os.path.exists(cache_file):
                try:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        strategic_scores = json.load(f)
                    print(f"已从缓存加载 {len(strategic_scores)} 个充电站的战略价值评分")
                    self.strategic_scores = strategic_scores
                except Exception as e:
                    print(f"加载缓存文件失败: {e}")
                    strategic_scores = {station: 50 for station in self.poi_vectors.index}  # 使用默认值
                    self.strategic_scores = strategic_scores
            else:
                print("未找到战略价值评分缓存，使用默认值")
                strategic_scores = {station: 50 for station in self.poi_vectors.index}  # 使用默认值
                self.strategic_scores = strategic_scores

        # 如果有订单数据，计算业绩评分
        if self.order_analyzer is not None and self.performance_scores is not None:
            # 创建DataFrame，包含POI评分、业绩评分和战略价值评分
            stations = list(poi_scores.keys())
            poi_score_list = list(poi_scores.values())
            strategic_score_list = [strategic_scores.get(station, 0) for station in stations]

            # 获取业绩评分（如果有）
            performance_scores = []
            for station in stations:
                if station in self.performance_scores:
                    performance_scores.append(self.performance_scores[station])
                else:
                    performance_scores.append(None)

            # 计算综合评分（POI评分、业绩评分和战略价值评分的加权平均）
            combined_scores = []
            for i in range(len(stations)):
                if performance_scores[i] is not None:
                    # 综合评分 = 0.5 * POI评分 + 0.3 * 业绩评分 + 0.2 * 战略价值评分
                    combined_scores.append(
                        0.5 * poi_score_list[i] +
                        0.3 * performance_scores[i] +
                        0.2 * strategic_score_list[i]
                    )
                else:
                    # 如果没有业绩评分，综合评分为POI评分和战略价值评分的加权平均
                    combined_scores.append(0.7 * poi_score_list[i] + 0.3 * strategic_score_list[i])

            self.scores_df = pd.DataFrame({
                'station': stations,
                'poi_score': poi_score_list,
                'performance_score': performance_scores,
                'strategic_score': strategic_score_list,
                'combined_score': combined_scores
            })

            # 按综合得分降序排序
            self.scores_df = self.scores_df.sort_values('combined_score', ascending=False)
        else:
            # 创建DataFrame，包含POI评分和战略价值评分
            stations = list(poi_scores.keys())
            poi_score_list = list(poi_scores.values())
            strategic_score_list = [strategic_scores.get(station, 0) for station in stations]

            # 计算综合评分（POI评分和战略价值评分的加权平均）
            combined_scores = []
            for i in range(len(stations)):
                combined_scores.append(0.7 * poi_score_list[i] + 0.3 * strategic_score_list[i])

            self.scores_df = pd.DataFrame({
                'station': stations,
                'poi_score': poi_score_list,
                'strategic_score': strategic_score_list,
                'combined_score': combined_scores
            })

            # 按综合得分降序排序
            self.scores_df = self.scores_df.sort_values('combined_score', ascending=False)

        print("多维度评分框架构建完成")
        return self.scores_df

    def train_performance_model_with_two_quarters(self):
        """使用两个季度的数据训练性能预测模型"""
        if self.order_analyzer is None or self.order_analyzer_q1 is None or self.poi_vectors is None:
            print("缺少订单数据或POI向量，无法训练模型")
            return None, None

        print("正在使用两个季度的数据训练性能预测模型...")

        # 计算场站业绩指标
        if not hasattr(self.order_analyzer, 'station_metrics') or self.order_analyzer.station_metrics is None:
            self.order_analyzer.calculate_station_metrics()

        if not hasattr(self.order_analyzer_q1, 'station_metrics') or self.order_analyzer_q1.station_metrics is None:
            self.order_analyzer_q1.calculate_station_metrics()

        if self.order_analyzer.station_metrics is None or self.order_analyzer_q1.station_metrics is None:
            print("无法获取场站业绩指标，跳过模型训练")
            return None, None

        # 找到两个季度和POI数据中共有的站
        q2_stations = set(self.order_analyzer.station_metrics.index)
        q1_stations = set(self.order_analyzer_q1.station_metrics.index)
        poi_stations = set(self.poi_vectors.index)

        common_stations = list(q2_stations & q1_stations & poi_stations)
        print(f"找到 {len(common_stations)} 个同时有两个季度数据和POI数据的充电站")

        if len(common_stations) < 10:
            print("共有站数量太少，无法训练可靠的模型")
            return None, None

        # 准备训练数据
        X = []
        y = []

        # 构建增强特征
        for station in common_stations:
            # 基本特征：POI向量
            station_features = list(self.poi_vectors.loc[station].values)

            # 添加第一季度营业额作为特征
            q1_revenue = self.order_analyzer_q1.station_metrics.loc[station, 'total_revenue']
            station_features.append(q1_revenue)

            # 计算增长率特征
            q2_revenue = self.order_analyzer.station_metrics.loc[station, 'total_revenue']
            growth_rate = (q2_revenue - q1_revenue) / max(1, q1_revenue)  # 防止除零
            station_features.append(growth_rate)

            # 添加到训练数据
            X.append(station_features)
            y.append(q2_revenue)

        # 转换为numpy数组
        X = np.array(X)
        y = np.array(y)

        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # 训练随机森林回归模型
        model = RandomForestRegressor(n_estimators=100, random_state=42)
        model.fit(X_train, y_train)

        # 评估模型
        y_pred = model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        print(f"两季度模型评估结果：MSE = {mse:.2f}, R² = {r2:.2f}")

        # 保存模型
        self.performance_model = model

        # 计算特征重要性
        # 注意：我们需要分开处理POI特征和其他特征
        poi_feature_names = list(self.poi_vectors.columns)
        additional_feature_names = ['q1_revenue', 'growth_rate']
        all_feature_names = poi_feature_names + additional_feature_names

        feature_importance = pd.DataFrame({
            'feature': all_feature_names,
            'importance': model.feature_importances_
        }).sort_values('importance', ascending=False)

        # 保存特征重要性
        feature_importance.to_csv('output/feature_importance_two_quarters.csv', index=False)
        print("特征重要性已保存到 output/feature_importance_two_quarters.csv")

        # 可视化特征重要性（前20个）
        top_features = feature_importance.head(20)
        plt.figure(figsize=(12, 8))
        plt.barh(top_features['feature'], top_features['importance'], color='skyblue')
        plt.xlabel('重要性')
        plt.title('POI特征重要性（Top 20）- 两季度模型')
        plt.gca().invert_yaxis()  # 使最重要的在顶部
        plt.grid(axis='x', linestyle='--', alpha=0.7)
        plt.tight_layout()

        plt.savefig('output/feature_importance_two_quarters.png', dpi=300)
        plt.close()

        print("特征重要性可视化已保存到 output/feature_importance_two_quarters.png")

        return self.performance_model, feature_importance

    def optimize_weights_with_orders(self):
        """使用订单数据优化POI权重"""
        if self.order_analyzer is None or self.poi_vectors is None:
            print("缺少订单数据或POI向量，无法优化权重")
            return

        print("正在使用订单数据优化POI权重...")

        # 计算场站业绩指标
        if not hasattr(self.order_analyzer, 'station_metrics') or self.order_analyzer.station_metrics is None:
            self.order_analyzer.calculate_station_metrics()

        if self.order_analyzer.station_metrics is None:
            print("无法获取场站业绩指标，跳过权重优化")
            return

        # 如果有两个季度的数据，使用两个季度的数据训练模型
        if self.use_two_quarters and self.order_analyzer_q1 is not None:
            model, feature_importance = self.train_performance_model_with_two_quarters()
        else:
            # 训练性能预测模型
            model, feature_importance = self.order_analyzer.train_performance_model(self.poi_vectors)

        if model is None:
            print("模型训练失败，跳过权重优化")
            return

        # 优化权重
        self.optimized_weights = self.order_analyzer.optimize_weights(self.poi_vectors)

        if self.optimized_weights is None:
            print("权重优化失败")
            return

        # 将优化后的权重应用到当前权重字典
        if self.weights is None:
            self.weights = self._build_weights_from_used_poi()

        # 更新权重
        for code, weight in self.optimized_weights.items():
            if code in self.weights:
                self.weights[code] = weight

        print(f"已使用订单数据优化 {len(self.optimized_weights)} 个POI权重")

        # 计算业绩评分
        self.calculate_performance_scores()

    def calculate_strategic_value_score(self, station_name=None, coordinates=None):
        """计算充电站的战略价值评分（简化版）

        参数:
            station_name: 现有充电站名称
            coordinates: 新充电站的坐标 (longitude, latitude)

        返回:
            战略价值评分 (0-100)
        """
        # 初始化评分组件
        brand_exposure_score = 0
        network_coverage_score = 0

        # 根据是评估现有站点还是新站点选择不同的计算方法
        if station_name is not None:
            # 评估现有充电站
            location = self._get_station_location(station_name)
        elif coordinates is not None:
            # 评估新的坐标位置
            location = coordinates
        else:
            print("错误：必须提供站点名称或坐标")
            return 0

        # 计算品牌曝光度 (0-100)
        brand_exposure_score = self._calculate_brand_exposure_value(location)

        # 计算网络覆盖价值 (0-100)
        network_coverage_score = self._calculate_network_coverage_value(location)

        # 计算加权总分
        weights = {
            'brand_exposure': 0.5,  # 品牌曝光度权重
            'network_coverage': 0.5  # 网络覆盖价值权重
        }

        strategic_value_score = (
            weights['brand_exposure'] * brand_exposure_score +
            weights['network_coverage'] * network_coverage_score
        )

        return strategic_value_score

    def _calculate_brand_exposure_value(self, location):
        """计算品牌曝光度

        评估该位置对品牌曝光和影响力的贡献，着重于商务型曝光
        """
        # 获取位置周边的POI数据
        high_traffic_poi_types = {
            '050000': 10,  # 餐饮服务
            '060100': 10,  # 购物中心
            '080300': 8,   # 休闲娱乐
            '090000': 9    # 旅游景点
        }

        traffic_keys =  '|'.join(high_traffic_poi_types.keys())
        POI_keys = traffic_keys + '|180300|150000|120200'
        poi_data = self._get_surrounding_pois(location, POI_keys, radius=1.0)  # 1公里半径内

        if not poi_data or 'pois' not in poi_data:
            return 50  # 默认中等曝光度

        pois = poi_data['pois']

        # 计算周边人流量指标
        foot_traffic_score = 0

        # 人流量高的POI类型及其权重（着重于商务型曝光）


        # 统计各类POI的数量
        poi_counts = {}
        for poi in pois:
            typecode = poi.get('typecode', '')
            if typecode:
                # 检查是否匹配指定的POI类型
                for poi_type in high_traffic_poi_types.keys():
                    if typecode.startswith(poi_type[:2]):
                        poi_counts[poi_type] = poi_counts.get(poi_type, 0) + 1

        # 计算人流量得分
        total_weight = sum(high_traffic_poi_types.values())
        weighted_count = 0

        for poi_type, weight in high_traffic_poi_types.items():
            count = poi_counts.get(poi_type, 0)
            weighted_count += count * weight

        # 归一化人流量得分（假设最高期望值为每类POI平均5个）
        max_expected_weighted_count = 5 * total_weight
        foot_traffic_score = min(100, (weighted_count / max_expected_weighted_count) * 100)

        # 计算位置可见性指标
        visibility_score = 0

        # 检查是否在交通枢纽附近
        near_transport_hub = False
        for poi in pois:
            typecode = poi.get('typecode', '')
            # 高速服务区
            if typecode.startswith('1803'):
                near_transport_hub = True
                break
            # 地铁站
            elif typecode.startswith('1503'):
                near_transport_hub = True
                break
            # 火车站
            elif typecode.startswith('1502'):
                near_transport_hub = True
                break
            # 机场
            elif typecode.startswith('1501'):
                near_transport_hub = True
                break

        # 检查是否在商业中心区
        in_business_district = sum(1 for poi in pois if poi.get('typecode', '').startswith('1202')) >= 3

        # 计算可见性得分
        if near_transport_hub:
            visibility_score += 60  # 交通枢纽附近的可见性更高
        if in_business_district:
            visibility_score += 40  # 商业中心区的可见性也很高

        # 限制最高分为100
        visibility_score = min(100, visibility_score)

        # 综合人流量和可见性得分
        brand_exposure_score = 0.6 * foot_traffic_score + 0.4 * visibility_score

        return brand_exposure_score

    def _calculate_network_coverage_value(self, location):
        """计算网络覆盖价值

        评估该位置对整体充电网络覆盖的贡献
        """
        # 获取所有现有充电站的位置
        existing_stations = self._get_all_station_locations()

        # 如果没有现有站点数据，返回默认值
        if not existing_stations:
            return 50

        # 计算该位置到所有现有充电站的距离
        distances = []
        for station_loc in existing_stations:
            distance = self._calculate_distance(location, station_loc)
            distances.append(distance)

        # 排序并取最近的5个站点
        distances.sort()
        nearest_5_distances = distances[:5] if len(distances) >= 5 else distances
        nearest_5_avg_distance = sum(nearest_5_distances) / len(nearest_5_distances)

        # 计算覆盖半径（假设理想覆盖半径为5公里）
        ideal_coverage_radius = 5.0  # 公里

        # 计算覆盖得分
        # 如果平均距离太近（<2公里），表示该区域已有较好覆盖，战略价值较低
        # 如果平均距离适中（2-10公里），表示该位置可以很好地扩展网络覆盖，战略价值高
        # 如果平均距离太远（>10公里），可能表示该区域需求不足，战略价值中等
        if nearest_5_avg_distance < 2.0:
            coverage_score = 40  # 覆盖重叠，价值较低
        elif nearest_5_avg_distance <= 10.0:
            # 在2-10公里范围内，距离越接近理想覆盖半径，分数越高
            coverage_score = 100 - abs(nearest_5_avg_distance - ideal_coverage_radius) * 10
        else:
            # 距离太远，可能是孤立点
            coverage_score = 60

        # 计算覆盖密度指标
        # 统计20公里范围内的站点数量
        region_radius = 20.0  # 公里
        stations_in_region = sum(1 for d in distances if d <= region_radius)

        # 计算密度得分（假设理想密度为每20公里半径内有10-15个站点）
        if stations_in_region < 5:
            # 站点太少，需要增加覆盖
            density_score = 80
        elif stations_in_region <= 15:
            # 站点数量适中，最理想的情况
            density_score = 100
        else:
            # 站点过多，可能过度竞争
            density_score = 100 - (stations_in_region - 15) * 5

        # 确保密度得分在0-100范围内
        density_score = max(0, min(100, density_score))

        # 综合覆盖得分和密度得分
        network_coverage_score = 0.7 * coverage_score + 0.3 * density_score

        return network_coverage_score

    def _get_station_location(self, station_name):
        """获取充电站的坐标位置"""
        if station_name in self.raw_poi_data:
            station_info = self.raw_poi_data[station_name]
            return (station_info['longitude'], station_info['latitude'])
        return None

    def _get_all_station_locations(self):
        """获取所有充电站的位置"""
        locations = []
        for station_name, station_info in self.raw_poi_data.items():
            locations.append((station_info['longitude'], station_info['latitude']))
        return locations

    def _calculate_distance(self, location1, location2):
        """计算两个坐标点之间的距离（公里）"""
        # 使用Haversine公式计算球面距离
        lon1, lat1 = location1
        lon2, lat2 = location2

        # 将经纬度转换为弧度
        lon1, lat1, lon2, lat2 = map(math.radians, [lon1, lat1, lon2, lat2])

        # Haversine公式
        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        r = 6371  # 地球半径（公里）

        return c * r

    def _get_surrounding_pois(self, location, keys, radius=1.0, max_retries=3, retry_delay=1.0):
        """获取周边POI数据

        参数:
            location: 坐标 (longitude, latitude)
            keys: POI类型编码，多个类型用"|"分隔
            radius: 搜索半径（公里）
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
        """
        # 生成缓存键
        cache_key = f"{location[0]},{location[1]}_{keys}_{radius}"

        # 检查缓存
        if cache_key in self.poi_cache:
            return self.poi_cache[cache_key]

        # 使用高德地图API的周边搜索服务
        url = f"https://restapi.amap.com/v3/place/around"
        params = {
            "key": self.api_key,
            "location": f"{location[0]},{location[1]}",
            "radius": int(radius * 1000),  # 转换为米
            "types": keys,
            "extensions": "all",
            "output": "json"
        }

        # 实现重试机制
        for retry in range(max_retries):
            try:
                # 添加随机延迟，避免请求过于集中
                if retry > 0:
                    import time
                    import random
                    # 随机延迟，避免所有请求同时发送
                    delay = retry_delay * (1 + random.random())
                    time.sleep(delay)

                response = requests.get(url, params=params)
                data = response.json()

                if data.get("status") == "1":
                    # 缓存结果
                    self.poi_cache[cache_key] = data
                    return data
                elif data.get("info") == "CUQPS_HAS_EXCEEDED_THE_LIMIT" and retry < max_retries - 1:
                    print(f"API调用频率超限，正在重试 ({retry+1}/{max_retries})...")
                    continue
                else:
                    print(f"获取周边POI失败: {data.get('info')}")
                    return None
            except Exception as e:
                print(f"获取周边POI时出错: {e}")
                if retry < max_retries - 1:
                    print(f"正在重试 ({retry+1}/{max_retries})...")
                    continue
                return None

        return None

    def calculate_performance_scores(self):
        """计算场站的业绩评分"""
        if self.order_analyzer is None or not hasattr(self.order_analyzer, 'station_metrics') or self.order_analyzer.station_metrics is None:
            print("无法获取场站业绩指标，跳过业绩评分计算")
            return

        print("正在计算场站业绩评分...")

        # 使用总营业额作为业绩评分的基础
        metrics = self.order_analyzer.station_metrics

        # 对营业额进行对数变换，减少极端值的影响
        # 添加1是为了处理可能的零值
        log_revenues = np.log1p(metrics['total_revenue'])

        # 记录对数变换后的最大和最小值，用于预测新场站的业绩评分
        self.log_revenue_max = log_revenues.max()
        self.log_revenue_min = log_revenues.min()

        # 标准化对数变换后的营业额到 0-100 范围
        if self.log_revenue_max > self.log_revenue_min:
            # 计算标准化评分
            self.performance_scores = {}
            for station, row in metrics.iterrows():
                log_revenue = np.log1p(row['total_revenue'])
                normalized_score = 100 * (log_revenue - self.log_revenue_min) / (self.log_revenue_max - self.log_revenue_min)
                self.performance_scores[station] = normalized_score

            print(f"已计算 {len(self.performance_scores)} 个场站的业绩评分")
        else:
            print("所有场站的营业额相同，无法计算有意义的业绩评分")
            self.performance_scores = None

    def visualize_scores(self):
        """可视化场站得分"""
        if not hasattr(self, 'scores_df'):
            self.build_scoring_framework()

        # 检查是否有综合评分
        has_combined_score = 'combined_score' in self.scores_df.columns
        score_column = 'combined_score' if has_combined_score else 'score' if 'score' in self.scores_df.columns else 'poi_score'

        # 只显示得分最高的20个场站
        top_stations = self.scores_df.head(20).copy()

        # 创建柱状图
        plt.figure(figsize=(14, 10))

        if has_combined_score:
            # 创建多维度柱状图
            x = np.arange(len(top_stations))
            width = 0.3

            # 绘制POI评分
            bars1 = plt.bar(x - width, top_stations['poi_score'], width, label='POI评分', color='skyblue')

            # 绘制业绩评分（如果有）
            if 'performance_score' in top_stations.columns:
                valid_mask = ~top_stations['performance_score'].isna()
                bars2 = plt.bar(x[valid_mask], top_stations.loc[valid_mask, 'performance_score'],
                                width, label='业绩评分', color='lightgreen')

            # 绘制战略价值评分（如果有）
            if 'strategic_score' in top_stations.columns:
                bars3 = plt.bar(x, top_stations['strategic_score'], width, label='战略价值评分', color='orange')

            # 绘制综合评分
            bars4 = plt.bar(x + width, top_stations['combined_score'], width, label='综合评分', color='salmon')

            # 设置x轴刻度和标签
            plt.xticks(x, top_stations['station'], rotation=45, ha='right')
            plt.legend()

            # 设置标题和轴标签
            plt.xlabel('场站（仅显示得分最高的20个）', fontsize=12)
            plt.ylabel('得分', fontsize=12)
            plt.title('场站多维度评分排名（Top 20）', fontsize=14)
        else:
            # 创建单一评分柱状图
            bars = plt.bar(range(len(top_stations)), top_stations[score_column])

            # 设置x轴刻度和标签
            plt.xticks(range(len(top_stations)), top_stations['station'], rotation=45, ha='right')

            # 在柱状图上方显示得分
            for i, bar in enumerate(bars):
                plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.5,
                        f'{top_stations[score_column].iloc[i]:.1f}',
                        ha='center', va='bottom', fontsize=9)

            # 设置标题和轴标签
            plt.xlabel('场站（仅显示得分最高的20个）', fontsize=12)
            plt.ylabel('得分', fontsize=12)
            plt.title('场站综合得分排名（Top 20）', fontsize=14)
            plt.ylim(0, top_stations[score_column].max() * 1.1)  # 设置y轴上限，留出空间显示数值

        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig('output/station_scores_top20.png', dpi=300)

        # 创建得分分布直方图
        plt.figure(figsize=(10, 6))

        if has_combined_score:
            # 创建多维度直方图
            hist_data = [self.scores_df['poi_score']]
            hist_colors = ['skyblue']
            hist_labels = ['POI评分']

            # 添加业绩评分（如果有）
            if 'performance_score' in self.scores_df.columns:
                hist_data.append(self.scores_df['performance_score'].dropna())
                hist_colors.append('lightgreen')
                hist_labels.append('业绩评分')

            # 添加战略价值评分（如果有）
            if 'strategic_score' in self.scores_df.columns:
                hist_data.append(self.scores_df['strategic_score'])
                hist_colors.append('orange')
                hist_labels.append('战略价值评分')

            # 添加综合评分
            hist_data.append(self.scores_df['combined_score'])
            hist_colors.append('salmon')
            hist_labels.append('综合评分')

            plt.hist(hist_data, bins=20, color=hist_colors, label=hist_labels, alpha=0.7, edgecolor='black')
            plt.legend()
            plt.title('场站多维度评分分布', fontsize=14)
        else:
            # 创建单一评分直方图
            plt.hist(self.scores_df[score_column], bins=20, color='skyblue', edgecolor='black')
            plt.title('场站得分分布', fontsize=14)

        plt.xlabel('得分区间', fontsize=12)
        plt.ylabel('场站数量', fontsize=12)
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig('output/station_scores_distribution.png', dpi=300)

        # 创建箱线图
        plt.figure(figsize=(12, 8))

        if has_combined_score:
            # 创建多维度箱线图
            data = [self.scores_df['poi_score']]
            labels = ['POI评分']
            colors = ['skyblue']

            # 添加业绩评分（如果有）
            if 'performance_score' in self.scores_df.columns:
                data.append(self.scores_df['performance_score'].dropna())
                labels.append('业绩评分')
                colors.append('lightgreen')

            # 添加战略价值评分（如果有）
            if 'strategic_score' in self.scores_df.columns:
                data.append(self.scores_df['strategic_score'])
                labels.append('战略价值评分')
                colors.append('orange')

            # 添加综合评分
            data.append(self.scores_df['combined_score'])
            labels.append('综合评分')
            colors.append('salmon')

            # 创建箱线图，并单独设置每个箱子的颜色
            box_plot = plt.boxplot(data, labels=labels, patch_artist=True)
            for patch, color in zip(box_plot['boxes'], colors):
                patch.set_facecolor(color)
            plt.title('场站多维度评分统计分布', fontsize=14)
        else:
            # 创建单一评分箱线图
            plt.boxplot(self.scores_df[score_column], vert=True, patch_artist=True,
                       boxprops=dict(facecolor='skyblue'))
            plt.title('场站得分统计分布', fontsize=14)

        plt.ylabel('得分', fontsize=12)
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig('output/station_scores_boxplot.png', dpi=300)

        plt.close('all')

        print("场站得分可视化已保存为多个文件：")
        print("- output/station_scores_top20.png：得分最高的20个场站")
        print("- output/station_scores_distribution.png：得分分布直方图")
        print("- output/station_scores_boxplot.png：得分箱线图")

    def get_strategic_score(self, station_name):
        """获取场站的战略价值评分，如果已计算则从缓存获取，否则计算并缓存

        参数:
            station_name: 场站名称

        返回:
            战略价值评分 (0-100)
        """
        # 检查是否已有缓存
        if hasattr(self, 'strategic_scores') and self.strategic_scores and station_name in self.strategic_scores:
            return self.strategic_scores[station_name]

        # 计算并缓存
        score = self.calculate_strategic_value_score(station_name=station_name)
        if not hasattr(self, 'strategic_scores') or self.strategic_scores is None:
            self.strategic_scores = {}
        self.strategic_scores[station_name] = score

        return score

    def visualize_similarity(self, station_name):
        """可视化与指定场站的相似度"""
        if self.similarity_df is None:
            self.calculate_similarity()

        if station_name not in self.similarity_df.index:
            print(f"场站 '{station_name}' 不在数据集中")
            return

        # 获取与给定场站的相似度
        similarities = self.similarity_df[station_name].sort_values(ascending=False)

        # 排除自身
        similarities = similarities[similarities.index != station_name]

        # 取前10个最相似的场站
        top_similar = similarities.head(10)

        plt.figure(figsize=(14, 8))
        bars = plt.bar(range(len(top_similar)), top_similar.values)

        # 设置x轴刻度和标签
        plt.xticks(range(len(top_similar)), top_similar.index, rotation=45, ha='right')

        # 在柱状图上方显示相似度值
        for i, bar in enumerate(bars):
            plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.01,
                    f'{top_similar.iloc[i]:.3f}',
                    ha='center', va='bottom', fontsize=9)

        plt.xlabel('场站', fontsize=12)
        plt.ylabel('相似度', fontsize=12)
        plt.title(f'与 {station_name} 最相似的场站 (Top 10)', fontsize=14)
        plt.ylim(0, top_similar.max() * 1.1)  # 设置y轴上限，留出空间显示数值
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig(f'output/similarity_{station_name}_top10.png', dpi=300)
        plt.close()

        # 创建热力图展示相似度矩阵
        plt.figure(figsize=(12, 10))

        # 选取相似度最高的20个场站的相似度矩阵
        top20_stations = similarities.head(20).index.tolist()
        top20_stations.append(station_name)  # 添加当前场站

        # 提取子矩阵
        sub_matrix = self.similarity_df.loc[top20_stations, top20_stations]

        # 绘制热力图
        plt.imshow(sub_matrix, cmap='YlOrRd', interpolation='nearest')
        plt.colorbar(label='相似度')

        # 设置坐标轴
        plt.xticks(range(len(top20_stations)), top20_stations, rotation=90)
        plt.yticks(range(len(top20_stations)), top20_stations)

        plt.title(f'与 {station_name} 相关的场站相似度热力图', fontsize=14)
        plt.tight_layout()
        plt.savefig(f'output/similarity_{station_name}_heatmap.png', dpi=300)
        plt.close()

        print(f"与 {station_name} 的相似度可视化已保存为多个文件：")
        print(f"- output/similarity_{station_name}_top10.png：柱状图展示最相似的10个场站")
        print(f"- output/similarity_{station_name}_heatmap.png：热力图展示相似度矩阵")

    def predict_new_station_score(self, new_poi_data, top_n=5):
        """预测新场站的得分"""
        # 找到最相似的场站
        similar_stations = self.predict_new_station(new_poi_data, top_n)

        if similar_stations is None or len(similar_stations) == 0:
            print("无法找到相似的场站")
            return None

        # 获取相似场站的得分和坐标信息
        similar_station_scores = []
        for station in similar_stations.index:
            score = self.evaluate_station_score(station)
            coords = self._get_station_coordinates(station)
            similar_station_scores.append((station, score, coords))

        # 计算加权平均得分（根据相似度加权）
        total_similarity = similar_stations.sum()
        weighted_score = 0

        for i, (station, score, coords) in enumerate(similar_station_scores):
            similarity = similar_stations.iloc[i]
            weighted_score += score * (similarity / total_similarity)

        # 直接计算新场站的原始得分
        new_vector = self._process_new_station_poi(new_poi_data)
        raw_score = 0
        for typecode, count in new_vector.items():
            if count > 0:
                # 获取大类编码（前两位）
                large_category_code = typecode[:2] + '0000'

                # 获取中类编码（前四位）
                medium_category_code = typecode[:4] + '00'

                # 优先级：具体编码 > 中类编码 > 大类编码 > 默认权重
                weight = self.weights.get(typecode,
                          self.weights.get(medium_category_code,
                            self.weights.get(large_category_code, 0.5)))

                # 累加得分
                raw_score += count * weight

        # 确保已经计算了所有场站的原始得分
        if not hasattr(self, 'max_raw_score'):
            self._calculate_all_station_raw_scores()

        # 使用现有场站的最高得分作为归一化基准
        direct_score = (raw_score / self.max_raw_score) * 100 if self.max_raw_score > 0 else 0

        # 取加权得分和直接计算得分的平均值，并限制在100分以内
        final_poi_score = min(100, max(0, (weighted_score + direct_score) / 2))

        print(f"预测的新场站POI得分: {final_poi_score:.2f}")

        # 计算战略价值评分
        # 从新POI数据中提取坐标
        coordinates = None
        if 'location' in new_poi_data:
            location_str = new_poi_data['location']
            if isinstance(location_str, str) and ',' in location_str:
                lng, lat = location_str.split(',')
                coordinates = (float(lng), float(lat))
            elif isinstance(location_str, dict) and 'lng' in location_str and 'lat' in location_str:
                coordinates = (location_str['lng'], location_str['lat'])

        # 如果无法从location字段获取坐标，尝试从第一个POI获取
        if coordinates is None and 'pois' in new_poi_data and len(new_poi_data['pois']) > 0:
            first_poi = new_poi_data['pois'][0]
            if 'location' in first_poi:
                location_str = first_poi['location']
                if isinstance(location_str, str) and ',' in location_str:
                    lng, lat = location_str.split(',')
                    coordinates = (float(lng), float(lat))

        # 计算战略价值评分，并限制在100分以内
        strategic_score = 0
        if coordinates:
            raw_strategic_score = self.calculate_strategic_value_score(coordinates=coordinates)
            strategic_score = min(100, max(0, raw_strategic_score))
            print(f"预测的新场站战略价值评分: {strategic_score:.2f}")
        else:
            print("警告：无法获取新场站的坐标，无法计算战略价值评分")
            # 使用相似站点的平均战略价值评分
            if hasattr(self, 'strategic_scores') and self.strategic_scores:
                strategic_scores_sum = 0
                count = 0
                for station in similar_stations.index:
                    # 使用缓存机制获取战略价值评分
                    station_score = self.get_strategic_score(station)
                    strategic_scores_sum += station_score
                    count += 1
                if count > 0:
                    strategic_score = strategic_scores_sum / count
                    print(f"使用相似站点的平均战略价值评分: {strategic_score:.2f}")
                else:
                    strategic_score = 50  # 默认中等战略价值
            else:
                strategic_score = 50  # 默认中等战略价值

        # 如果有订单数据，预测业绩
        predicted_revenue = None
        predicted_performance_score = None
        # 默认只有POI评分和战略价值评分，限制在100分以内
        combined_score = min(100, max(0, 0.7 * final_poi_score + 0.3 * strategic_score))

        # 使用两个季度的数据进行预测
        if self.use_two_quarters and self.order_analyzer is not None and self.order_analyzer_q1 is not None and hasattr(self, 'performance_model') and self.performance_model is not None:
            # 处理新场站的POI向量
            new_vector = self._process_new_station_poi(new_poi_data)

            # 预测新场站的营业额
            # 注意：两季度模型需要额外的特征（第一季度营业额和增长率）
            # 由于新场站没有这些数据，我们使用相似场站的平均值

            # 计算相似场站的第一季度营业额平均值
            q1_revenue_avg = 0
            growth_rate_avg = 0
            count = 0

            for station in similar_stations.index:
                if station in self.order_analyzer_q1.station_metrics.index and station in self.order_analyzer.station_metrics.index:
                    q1_revenue = self.order_analyzer_q1.station_metrics.loc[station, 'total_revenue']
                    q2_revenue = self.order_analyzer.station_metrics.loc[station, 'total_revenue']
                    growth_rate = (q2_revenue - q1_revenue) / max(1, q1_revenue)  # 防止除零

                    q1_revenue_avg += q1_revenue
                    growth_rate_avg += growth_rate
                    count += 1

            if count > 0:
                q1_revenue_avg /= count
                growth_rate_avg /= count
            else:
                # 如果没有相似场站的数据，使用所有场站的平均值
                q1_revenue_avg = self.order_analyzer_q1.station_metrics['total_revenue'].mean()

                # 计算所有场站的平均增长率
                common_stations = list(set(self.order_analyzer.station_metrics.index) & set(self.order_analyzer_q1.station_metrics.index))
                growth_rates = []
                for station in common_stations:
                    q1_revenue = self.order_analyzer_q1.station_metrics.loc[station, 'total_revenue']
                    q2_revenue = self.order_analyzer.station_metrics.loc[station, 'total_revenue']
                    growth_rate = (q2_revenue - q1_revenue) / max(1, q1_revenue)
                    growth_rates.append(growth_rate)

                growth_rate_avg = np.mean(growth_rates) if growth_rates else 0

            # 构建增强特征向量
            features = list(new_vector.values)
            features.append(q1_revenue_avg)  # 添加第一季度营业额特征
            features.append(growth_rate_avg)  # 添加增长率特征

            # 使用两季度模型预测
            predicted_revenue = self.performance_model.predict([features])[0]
            print(f"使用两季度模型预测的新场站周营业额: {predicted_revenue:.2f}")
        elif self.order_analyzer is not None and hasattr(self.order_analyzer, 'performance_model') and self.order_analyzer.performance_model is not None:
            # 如果没有两季度数据，使用单季度模型
            # 处理新场站的POI向量
            new_vector = self._process_new_station_poi(new_poi_data)

            # 预测新场站的营业额
            predicted_revenue = self.order_analyzer.predict_new_station_performance(new_vector, self.poi_vectors)
            print(f"使用单季度模型预测的新场站周营业额: {predicted_revenue:.2f}")

            if predicted_revenue is not None:
                # 计算业绩评分
                if self.performance_scores is not None and len(self.performance_scores) > 0:
                    # 确保已经计算了对数变换后的营业额范围
                    if not hasattr(self, 'log_revenue_max') or not hasattr(self, 'log_revenue_min'):
                        self.calculate_performance_scores()

                    if hasattr(self, 'log_revenue_max') and hasattr(self, 'log_revenue_min'):
                        # 对预测营业额进行对数变换
                        log_predicted_revenue = np.log1p(predicted_revenue)

                        # 标准化对数变换后的预测营业额到 0-100 范围
                        predicted_performance_score = min(100, max(0, 100 * (log_predicted_revenue - self.log_revenue_min) / (self.log_revenue_max - self.log_revenue_min)))

                        # 计算综合评分（包含战略价值评分），限制在100分以内
                        raw_combined = 0.5 * final_poi_score + 0.3 * predicted_performance_score + 0.2 * strategic_score
                        combined_score = min(100, max(0, raw_combined))

                        print(f"预测的新场站周营业额: {predicted_revenue:.2f}")
                        print(f"预测的新场站业绩评分: {predicted_performance_score:.2f}")
                        print(f"预测的新场站综合评分: {combined_score:.2f}")
                    else:
                        # 如果无法计算业绩评分，使用POI评分和战略价值评分，限制在100分以内
                        combined_score = min(100, max(0, 0.7 * final_poi_score + 0.3 * strategic_score))

        # 给出建议
        if combined_score >= 40:
            recommendation = "非常适合"
        elif combined_score >= 30:
            recommendation = "适合"
        elif combined_score >= 20:
            recommendation = "一般"
        else:
            recommendation = "不太适合"

        print(f"建议: 该位置 {recommendation} 建设充电站")

        # 构建包含坐标信息的相似场站列表
        similar_stations_with_coords = []
        for station, score, coords in similar_station_scores:
            station_info = {
                'name': station,
                'score': score,
                'coordinates': coords
            }
            similar_stations_with_coords.append(station_info)

        # 返回预测结果
        prediction_results = {
            'poi_score': final_poi_score,
            'strategic_score': strategic_score,
            'performance_score': predicted_performance_score,
            'predicted_revenue': predicted_revenue,
            'combined_score': combined_score,
            'similar_stations': similar_stations_with_coords,
            'recommendation': recommendation
        }

        return prediction_results

    def _get_station_coordinates(self, station_name):
        """获取场站坐标信息"""
        try:
            # 从场站数据中获取坐标
            station_data_file = 'resource/final_stations_107.csv'
            if os.path.exists(station_data_file):
                station_data = pd.read_csv(station_data_file)

                # 查找对应的场站
                station_row = station_data[station_data['名称'] == station_name]
                if not station_row.empty:
                    longitude = station_row.iloc[0]['高德_经度']
                    latitude = station_row.iloc[0]['高德_纬度']
                    address = station_row.iloc[0]['地址']
                    return {
                        'longitude': longitude,
                        'latitude': latitude,
                        'address': address
                    }
        except Exception as e:
            print(f"获取场站 {station_name} 坐标信息失败: {e}")

        return None

    def save_results(self):
        """保存分析结果"""
        print("正在保存分析结果...")

        # 保存POI向量
        if self.poi_vectors is not None:
            # 确保 output 目录存在
            os.makedirs('output', exist_ok=True)
            self.poi_vectors.to_csv('output/poi_vectors.csv')
            print("POI向量已保存为 output/poi_vectors.csv")

        # 保存相似度矩阵
        if hasattr(self, 'similarity_df'):
            self.similarity_df.to_csv('output/similarity_matrix.csv')
            print("相似度矩阵已保存为 output/similarity_matrix.csv")

        # 保存得分
        if hasattr(self, 'scores_df'):
            self.scores_df.to_csv('output/station_scores.csv', index=False)
            print("场站得分已保存为 output/station_scores.csv")

        print("所有结果已保存")

def main():
    """主函数"""
    analyzer = StationAnalyzer()

    # 加载数据
    analyzer.load_data()

    # 构建POI向量
    analyzer.build_poi_vectors()

    # 计算相似度
    analyzer.calculate_similarity()

    # 构建评分框架
    analyzer.build_scoring_framework()

    # 可视化得分
    analyzer.visualize_scores()

    # 保存结果
    analyzer.save_results()

    print("\n分析完成！")

    # 示例：找出与特定场站最相似的场站
    sample_station = list(analyzer.poi_vectors.index)[0]
    similar_stations = analyzer.find_similar_stations(sample_station)
    print(f"\n与 {sample_station} 最相似的场站:")
    print(similar_stations)

    # 可视化相似度
    analyzer.visualize_similarity(sample_station)

if __name__ == "__main__":
    main()
