#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
充电站评估报告生成工具

此脚本用于生成详细的充电站评估HTML/PDF报告，包括：
1. 评估结果概览
2. 详细评分分析
3. 相似场站对比
4. 建议与结论

使用方法：
1. 基于已有评估图片生成报告：
   python generate_report.py --image output/坐标评估_116.407526_39.90403.png

2. 直接基于坐标生成报告（无需先生成评估图片）：
   python generate_report.py --longitude 116.407526 --latitude 39.90403 --radius 1000

3. 批量处理所有评估图片：
   python generate_report.py --batch

可选参数：
--format: 报告格式，支持html和pdf，默认为html
--no-llm: 不使用DeepSeek LLM生成报告内容（默认使用LLM）
--api-key: DeepSeek API密钥
--template: 报告模板类型，默认为basic（基础模板），可选full（完整模板）
"""

import os
import re
import json
import argparse
import shutil
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from datetime import datetime
from integrated_station_analyzer import IntegratedStationAnalyzer
from deepseek_api import DeepSeekAPI
import report_templates
from evaluate_coordinates import evaluate_coordinates, visualize_evaluation_results, fetch_poi_data_from_coordinates
import config

# 全局变量，用于防止重复打开浏览器
_browser_opened_in_generate_report = False

# 设置中文字体
try:
    import matplotlib_chinese_fonts
except ImportError:
    print("警告: 未找到中文字体配置文件，图表中的中文可能无法正确显示")
    print("建议先运行 python fix_font_display.py 修复字体问题")
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='充电站评估报告生成工具')
    parser.add_argument('--image', type=str, help='评估图片路径')
    parser.add_argument('--longitude', type=float, help='经度坐标')
    parser.add_argument('--latitude', type=float, help='纬度坐标')
    parser.add_argument('--radius', type=int, default=1000, help='POI搜索半径（米），默认1000米')
    parser.add_argument('--output', type=str, default=None, help='输出报告路径，默认为与图片同名或坐标相关的HTML文件')
    parser.add_argument('--format', type=str, default='html', choices=['html', 'pdf'], help='报告格式，支持html和pdf')
    parser.add_argument('--batch', action='store_true', help='批量生成报告，处理output目录下所有评估图片')
    parser.add_argument('--use-llm', action='store_true', default=True, help='使用DeepSeek LLM生成更详细的报告内容，默认启用')
    parser.add_argument('--no-llm', action='store_true', help='不使用DeepSeek LLM生成报告内容')
    parser.add_argument('--api-key', type=str, default=None, help='DeepSeek API密钥，如不提供则从配置文件或环境变量获取')
    parser.add_argument('--template', type=str, default='basic', choices=['basic', 'full'], help='报告模板类型，basic为基础模板(默认)，full为完整模板')
    parser.add_argument('--gaode-api-key', type=str, default=None, help='高德地图API密钥，如不提供则从配置文件获取')

    args = parser.parse_args()

    # 检查参数有效性
    if not args.batch and not args.image and (args.longitude is None or args.latitude is None):
        parser.error("必须提供--image参数、--longitude和--latitude参数，或使用--batch参数")

    # 如果未提供高德API密钥，从配置文件获取
    if args.gaode_api_key is None:
        args.gaode_api_key = config.GAODE_API_KEY

    # 处理 --no-llm 参数，它会覆盖 --use-llm 参数
    if args.no_llm:
        args.use_llm = False

    # 如果使用LLM但未提供API密钥，检查配置文件和环境变量
    if args.use_llm and not args.api_key:
        if not config.DEEPSEEK_API_KEY and not os.environ.get("DEEPSEEK_API_KEY"):
            print("警告: 未提供DeepSeek API密钥，将使用模板默认内容")
            args.use_llm = False

    return args

def extract_coordinates_from_filename(filename):
    """从文件名中提取坐标信息"""
    pattern = r'坐标评估_(\d+\.\d+)_(\d+\.\d+)\.png'
    match = re.search(pattern, filename)

    if match:
        longitude = float(match.group(1))
        latitude = float(match.group(2))
        return longitude, latitude

    return None, None

def load_evaluation_data(longitude, latitude):
    """加载评估数据"""
    # 尝试从JSON文件加载评估数据
    json_file = f"output/评估结果_{longitude}_{latitude}.json"

    if os.path.exists(json_file):
        with open(json_file, 'r', encoding='utf-8') as f:
            return json.load(f)

    # 如果没有JSON文件，尝试从scores_df加载数据
    if os.path.exists("output/station_scores.csv"):
        scores_df = pd.read_csv("output/station_scores.csv")

        # 查找最近的评估结果
        # 这里简化处理，实际应用中可能需要更复杂的逻辑
        return {
            "coordinates": {"longitude": longitude, "latitude": latitude},
            "poi_score": None,
            "strategic_score": None,
            "performance_score": None,
            "combined_score": None,
            "predicted_revenue": None,
            "recommendation": "未知",
            "similar_stations": []
        }

    # 如果没有任何数据，返回空结果
    return None

def generate_html_report(image_path, evaluation_data, use_llm=False, api_key=None, template_type='optimized'):
    """
    生成HTML格式的评估报告

    参数:
        image_path: 评估图片路径
        evaluation_data: 评估数据
        use_llm: 是否使用LLM生成更详细的报告内容
        api_key: DeepSeek API密钥
        template_type: 模板类型，'basic'、'full'或'optimized'
    """
    # 获取模板
    if template_type == 'basic':
        templates = report_templates.get_basic_templates()
    elif template_type == 'optimized':
        templates = report_templates.get_optimized_templates()
    else:
        templates = report_templates.get_all_templates()

    # 提取坐标信息
    longitude, latitude = extract_coordinates_from_filename(image_path)

    # 创建报告文件名
    report_file = f"output/充电站评估报告_{longitude}_{latitude}.html"

    # 创建HTML内容
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>充电站评估报告</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                color: #333;
            }}
            .container {{
                max-width: 1000px;
                margin: 0 auto;
                background: #fff;
                padding: 20px;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }}
            h1, h2, h3, h4 {{
                color: #2c3e50;
                margin-top: 20px;
            }}
            h1 {{
                text-align: center;
                padding-bottom: 10px;
                border-bottom: 2px solid #eee;
            }}
            .evaluation-image {{
                text-align: center;
                margin: 20px 0;
            }}
            .evaluation-image img {{
                max-width: 100%;
                height: auto;
                border: 1px solid #ddd;
            }}
            .overview {{
                background: #f9f9f9;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
            }}
            .scores {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-bottom: 20px;
            }}
            .score-item {{
                background: #f5f5f5;
                padding: 15px;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                margin: 15px 0;
            }}
            table, th, td {{
                border: 1px solid #ddd;
            }}
            th, td {{
                padding: 10px;
                text-align: left;
            }}
            th {{
                background-color: #f2f2f2;
            }}
            .risk-alert {{
                background-color: #fff8e1;
                border-left: 4px solid #ffc107;
                padding: 10px;
                margin: 15px 0;
            }}
            .mobile-charging-advice {{
                background-color: #e8f5e9;
                border-left: 4px solid #4caf50;
                padding: 10px;
                margin: 15px 0;
            }}
            .score-table {{
                width: 100%;
                margin-bottom: 15px;
            }}
            .poi-comparison, .core-indicators {{
                width: 100%;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>充电站评估报告</h1>

            <div class="evaluation-image">
                <img src="{os.path.basename(image_path)}" alt="评估结果可视化">
            </div>
    """

    # 添加报告内容
    if use_llm and api_key:
        # 使用LLM生成内容
        llm_content = generate_llm_content(evaluation_data, templates, api_key)
        html_content += llm_content
    else:
        # 使用模板生成内容
        html_content += generate_template_content(evaluation_data, templates, longitude, latitude)

    # 结束HTML
    html_content += """
        </div>
    </body>
    </html>
    """

    # 保存HTML文件
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(html_content)

    # 复制图片到输出目录（如果不在同一位置）
    output_image_path = os.path.join('output', os.path.basename(image_path))
    if os.path.abspath(image_path) != os.path.abspath(output_image_path):
        shutil.copy(image_path, output_image_path)

    return report_file

def generate_llm_content(evaluation_data, templates, api_key):
    """使用DeepSeek LLM生成报告内容"""
    try:
        # 初始化DeepSeek API
        deepseek_api = DeepSeekAPI(api_key)

        # 生成完整报告内容
        report_sections = deepseek_api.generate_full_report(evaluation_data, templates)

        # 将各部分内容组合成HTML
        html_content = ""
        for section_name, content in report_sections.items():
            html_content += content + "\n\n"

        return html_content
    except Exception as e:
        print(f"使用LLM生成内容时出错: {e}")
        # 如果LLM生成失败，回退到模板生成
        longitude = evaluation_data.get('longitude', 0)
        latitude = evaluation_data.get('latitude', 0)
        return generate_template_content(evaluation_data, templates, longitude, latitude)

def generate_template_content(evaluation_data, templates, longitude, latitude):
    """生成基于模板的报告内容"""
    content = ""

    # 添加概览部分
    if "概览" in templates:
        template = templates["概览"]

        # 准备评级
        poi_assessment = get_assessment(evaluation_data.get("poi_score", 0))
        strategic_assessment = get_assessment(evaluation_data.get("strategic_score", 0))
        performance_assessment = get_assessment(evaluation_data.get("performance_score", 0))
        combined_assessment = get_assessment(evaluation_data.get("combined_score", 0))

        # 填充模板
        content += template.format(
            longitude=longitude,
            latitude=latitude,
            poi_score=evaluation_data.get("poi_score", 0) or 0,
            strategic_score=evaluation_data.get("strategic_score", 0) or 0,
            performance_score=evaluation_data.get("performance_score", 0) or 0,
            combined_score=evaluation_data.get("combined_score", 0) or 0,
            predicted_revenue=evaluation_data.get("predicted_revenue", 0) or 0,
            recommendation=evaluation_data.get("recommendation", "未知"),
            poi_assessment=poi_assessment,
            strategic_assessment=strategic_assessment,
            performance_assessment=performance_assessment,
            combined_assessment=combined_assessment
        )

    # 添加POI分析部分
    if "POI分析" in templates:
        template = templates["POI分析"]

        # 准备POI密度数据（示例值，实际应从评估数据中获取）
        traffic_density = "中等"
        commercial_density = "较高"
        residential_density = "较高"
        avg_traffic_density = "高"
        avg_commercial_density = "高"
        avg_residential_density = "中等"
        poi_summary = "该位置周边POI分布较为丰富，特别是商业和住宅设施，但交通设施略显不足。"

        # 填充模板
        content += template.format(
            poi_score=evaluation_data.get("poi_score", 0) or 0,
            assessment=get_assessment(evaluation_data.get("poi_score", 0)),
            traffic_density=traffic_density,
            commercial_density=commercial_density,
            residential_density=residential_density,
            avg_traffic_density=avg_traffic_density,
            avg_commercial_density=avg_commercial_density,
            avg_residential_density=avg_residential_density,
            poi_summary=poi_summary
        )

    # 添加战略价值分析部分
    if "战略价值分析" in templates:
        template = templates["战略价值分析"]

        # 准备风险提示
        risk_note = "该区域竞争较为激烈，需注意差异化运营策略。"
        strategic_summary = "该位置战略价值适中，对品牌曝光有一定帮助，但需注意竞争风险。"

        # 填充模板
        content += template.format(
            strategic_score=evaluation_data.get("strategic_score", 0) or 0,
            assessment=get_assessment(evaluation_data.get("strategic_score", 0)),
            risk_note=risk_note,
            strategic_summary=strategic_summary
        )

    # 添加业绩分析部分
    if "业绩分析" in templates:
        template = templates["业绩分析"]

        # 准备业绩总结
        performance_summary = "基于相似场站的表现，该位置预期有良好的业绩表现，周营业额有望达到预期。"

        # 填充模板
        content += template.format(
            performance_score=evaluation_data.get("performance_score", 0) or 0,
            assessment=get_assessment(evaluation_data.get("performance_score", 0)),
            predicted_revenue=evaluation_data.get("predicted_revenue", 0) or 0,
            performance_summary=performance_summary
        )

    # 添加综合评分与建议部分
    if "综合评分与建议" in templates:
        template = templates["综合评分与建议"]

        # 准备核心指标和建议
        customer_potential = "中等"
        strategic_value = "适中"
        expected_return = f"{evaluation_data.get('predicted_revenue', 0) or 0:.2f}元/周"

        # 根据综合评分选择合适的充电车配置
        if (evaluation_data.get("combined_score", 0) or 0) >= 60:
            mobile_charging_advice = "建议配置规格2充电车(150kW)，以满足较高的充电需求。"
        else:
            mobile_charging_advice = "建议配置规格1充电车(75kW)，以满足基本需求。"

        # 填充模板
        content += template.format(
            customer_potential=customer_potential,
            strategic_value=strategic_value,
            expected_return=expected_return,
            mobile_charging_advice=mobile_charging_advice
        )

    return content

def get_assessment(score):
    """根据评分返回评估等级"""
    if score is None:
        return "未评估"
    if score >= 80:
        return "优秀"
    elif score >= 60:
        return "良好"
    elif score >= 40:
        return "一般"
    else:
        return "较差"

def generate_pdf_report(image_path, evaluation_data, use_llm=False, api_key=None, template_type='full'):
    """
    生成PDF格式的报告

    参数:
        image_path: 评估图片路径
        evaluation_data: 评估数据
        use_llm: 是否使用DeepSeek LLM生成更详细的报告内容
        api_key: DeepSeek API密钥
        template_type: 模板类型，'basic'或'full'
    """
    # 首先生成HTML报告
    html_report = generate_html_report(image_path, evaluation_data, use_llm, api_key, template_type)

    # 提取文件名（不含路径和扩展名）
    base_name = os.path.basename(image_path)
    file_name_without_ext = os.path.splitext(base_name)[0]

    # 创建PDF报告文件名
    pdf_report = f"output/{file_name_without_ext}_报告.pdf"

    try:
        # 尝试导入weasyprint
        from weasyprint import HTML

        # 将HTML转换为PDF
        HTML(html_report).write_pdf(pdf_report)

        return pdf_report
    except ImportError:
        print("警告: 未安装weasyprint，无法生成PDF报告")
        print("请安装weasyprint: pip install weasyprint")
        print("或者使用HTML格式的报告")

        return html_report

def process_coordinates(longitude, latitude, radius=1000, report_format='html', use_llm=False, api_key=None, template_type='full', gaode_api_key='9bc017d3ba3c32eb6fb2d9fe0d224d96'):
    """
    直接处理坐标，评估并生成报告

    参数:
        longitude: 经度
        latitude: 纬度
        radius: POI搜索半径（米）
        report_format: 报告格式，'html'或'pdf'
        use_llm: 是否使用DeepSeek LLM生成更详细的报告内容
        api_key: DeepSeek API密钥
        template_type: 模板类型，'basic'或'full'
        gaode_api_key: 高德地图API密钥
    """
    print(f"正在处理坐标 ({longitude}, {latitude})...")

    # 初始化集成分析器（使用新版场站评分系统）
    analyzer = IntegratedStationAnalyzer(
        order_data_file='resource/2025_Q2_10cols_107stations.csv',
        order_data_file_q1='resource/output_2025_Q1_10cols_107stations.csv',
        use_distance_weight=True,
        decay_factor=1000,
        max_distance=3000,
        min_weight=0.1
    )

    # 加载数据
    analyzer.load_data()

    # 构建集成评分框架
    analyzer.build_integrated_scoring_framework()

    # 评估坐标
    evaluation_data = evaluate_coordinates(longitude, latitude, analyzer, api_key=gaode_api_key, radius=radius)

    if evaluation_data is None:
        print(f"错误: 无法评估坐标 ({longitude}, {latitude})")
        return None

    # 生成评估图片
    image_path = f"output/坐标评估_{longitude}_{latitude}.png"
    if not os.path.exists(image_path):
        visualize_evaluation_results(evaluation_data, analyzer, save_path=image_path)

    # 生成报告
    if report_format == 'html':
        report_file = generate_html_report(image_path, evaluation_data, use_llm, api_key, template_type)
    else:
        report_file = generate_pdf_report(image_path, evaluation_data, use_llm, api_key, template_type)

    print(f"报告已生成: {report_file}")
    return report_file

def process_single_image(image_path, report_format='html', use_llm=False, api_key=None, template_type='full'):
    """
    处理单个图片，生成报告

    参数:
        image_path: 评估图片路径
        report_format: 报告格式，'html'或'pdf'
        use_llm: 是否使用DeepSeek LLM生成更详细的报告内容
        api_key: DeepSeek API密钥
        template_type: 模板类型，'basic'或'full'
    """
    # 检查图片是否存在
    if not os.path.exists(image_path):
        print(f"错误: 图片 {image_path} 不存在")
        return None

    # 从文件名中提取坐标
    longitude, latitude = extract_coordinates_from_filename(image_path)

    if longitude is None or latitude is None:
        print(f"错误: 无法从文件名 {image_path} 中提取坐标信息")
        return None

    print(f"从文件名中提取的坐标: ({longitude}, {latitude})")

    # 加载评估数据
    evaluation_data = load_evaluation_data(longitude, latitude)

    if evaluation_data is None:
        print("警告: 未找到评估数据，将生成简化报告")

    # 生成报告
    if report_format == 'html':
        report_file = generate_html_report(image_path, evaluation_data, use_llm, api_key, template_type)
    else:
        report_file = generate_pdf_report(image_path, evaluation_data, use_llm, api_key, template_type)

    print(f"报告已生成: {report_file}")
    return report_file

def generate_new_station_report(longitude, latitude, radius=1000, use_llm=True, api_key=None, template_type='basic'):
    """
    为新场站生成完整的分析报告（包括评估和可视化）

    参数:
        longitude: 经度
        latitude: 纬度
        radius: POI搜索半径（米）
        use_llm: 是否使用LLM生成报告内容
        api_key: DeepSeek API密钥
        template_type: 模板类型

    返回:
        报告文件路径
    """
    print(f"正在为新场站坐标 ({longitude}, {latitude}) 生成完整分析报告...")

    # 从配置文件获取API密钥
    if api_key is None:
        try:
            api_key = config.get_api_key('deepseek')
        except:
            print("警告: 未找到DeepSeek API密钥，将使用模板生成报告")

    gaode_api_key = config.get_api_key('gaode')

    # 直接处理坐标并生成报告
    report_file = process_coordinates(
        longitude,
        latitude,
        radius,
        'html',
        use_llm,
        api_key,
        template_type,
        gaode_api_key
    )

    if report_file:
        print(f"新场站分析报告已生成: {report_file}")

        # 尝试在浏览器中打开报告（防止重复打开）
        global _browser_opened_in_generate_report
        if not _browser_opened_in_generate_report:
            try:
                import webbrowser
                webbrowser.open(f"file://{os.path.abspath(report_file)}")
                _browser_opened_in_generate_report = True
                print("报告已在浏览器中打开")
            except:
                print(f"无法自动打开报告，请手动打开: {report_file}")
        else:
            print("浏览器已经打开过报告，跳过重复打开")

    return report_file

def main():
    """主函数"""
    args = parse_arguments()

    # 批量处理模式
    if args.batch:
        print("批量生成报告模式")

        # 获取output目录下所有评估图片
        output_dir = 'output'
        if not os.path.exists(output_dir):
            print(f"错误: 目录 {output_dir} 不存在")
            return

        # 查找所有评估图片
        image_files = []
        for file in os.listdir(output_dir):
            if file.startswith('坐标评估_') and file.endswith('.png'):
                image_files.append(os.path.join(output_dir, file))

        if not image_files:
            print(f"未找到任何评估图片")
            return

        print(f"找到 {len(image_files)} 个评估图片")

        # 处理每个图片
        generated_reports = []
        for image_file in image_files:
            print(f"\n处理图片: {image_file}")
            report_file = process_single_image(
                image_file,
                args.format,
                args.use_llm,
                args.api_key,
                args.template
            )
            if report_file:
                generated_reports.append(report_file)

        print(f"\n共生成 {len(generated_reports)} 个报告")

        # 尝试打开最后一个报告（防止重复打开）
        global _browser_opened_in_generate_report
        if generated_reports and not _browser_opened_in_generate_report:
            try:
                import webbrowser
                webbrowser.open(f"file://{os.path.abspath(generated_reports[-1])}")
                _browser_opened_in_generate_report = True
                print("最后一个报告已在浏览器中打开")
            except:
                print(f"无法自动打开报告，请手动打开: {generated_reports[-1]}")
        elif generated_reports and _browser_opened_in_generate_report:
            print("浏览器已经打开过报告，跳过重复打开")

    # 坐标处理模式
    elif args.longitude is not None and args.latitude is not None:
        print(f"坐标处理模式: ({args.longitude}, {args.latitude})")

        report_file = process_coordinates(
            args.longitude,
            args.latitude,
            args.radius,
            args.format,
            args.use_llm,
            args.api_key,
            args.template,
            args.gaode_api_key
        )

        # 尝试在浏览器中打开报告（防止重复打开）
        global _browser_opened_in_generate_report
        if report_file and not _browser_opened_in_generate_report:
            try:
                import webbrowser
                webbrowser.open(f"file://{os.path.abspath(report_file)}")
                _browser_opened_in_generate_report = True
                print("报告已在浏览器中打开")
            except:
                print(f"无法自动打开报告，请手动打开: {report_file}")
        elif report_file and _browser_opened_in_generate_report:
            print("浏览器已经打开过报告，跳过重复打开")

    # 单个图片处理模式
    else:
        report_file = process_single_image(
            args.image,
            args.format,
            args.use_llm,
            args.api_key,
            args.template
        )

        # 尝试在浏览器中打开报告（防止重复打开）
        global _browser_opened_in_generate_report
        if report_file and not _browser_opened_in_generate_report:
            try:
                import webbrowser
                webbrowser.open(f"file://{os.path.abspath(report_file)}")
                _browser_opened_in_generate_report = True
                print("报告已在浏览器中打开")
            except:
                print(f"无法自动打开报告，请手动打开: {report_file}")
        elif report_file and _browser_opened_in_generate_report:
            print("浏览器已经打开过报告，跳过重复打开")

if __name__ == "__main__":
    main()
